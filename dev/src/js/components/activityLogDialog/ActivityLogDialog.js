import React, { useState, useContext } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    Flex,
    Paragraph,
    InputSearchbox,
    Table,
    ModalDialogFooter,
    Button,
    ToastContext,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { CircleInfoOutline } from '@majoo-ui/icons';
import { useMediaQuery } from '~/utils/useMediaQuery';
import { usePartialState } from '~/utils/usePartialState';
import { getActivityLogV2 } from '~/data/product';
import { catchError } from '~/utils/helper';
import { ACTIVITY_LOG_TYPE, tableColumns } from './config';
import DetailDialog from './DetailDialog';

const ActivityLogDialog = ({ isOpen, onClose, logType, idCabang, filterBranch }) => {
    const { t } = useTranslation(['activityLog', 'translation']);
    const isInMobileView = useMediaQuery('(max-width: 1024px)');
    const { addToast } = useContext(ToastContext);
    const [activityLogs, setActivityLogs] = useState([]);
    const [filter, setFilter] = usePartialState({
        search: '',
    });
    const [detailDialogState, setDetailDialogState] = useState({
        open: false,
        logData: null,
    });

    const handleFetchActivityLogs = async () => {
        try {
            let outletId = idCabang;
    
            if (filterBranch) outletId = filterBranch;
    
            const res = await getActivityLogV2(outletId, `${logType}_v2`, {
                per_page: 1000,
            });
            if ('data' in res) {
                setActivityLogs(res.data);
            }
        } catch (err) {
            addToast({
                title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                description: catchError(err),
                variant: 'failed',
            });
        }
    };

    const filteredActivityLogs = activityLogs.filter(log => {
        if (
            filter.search &&
            !log.product_name.toLowerCase().includes(filter.search.toLowerCase()) &&
            !log.actor_name.toLowerCase().includes(filter.search.toLowerCase())
        )
            return false;
        return true;
    });

    const onShowDetail = data => {
        setDetailDialogState({
            open: true,
            logData: data,
        });
    };

    return (
        <React.Fragment>
            <ModalDialog
                open={isOpen}
                onOpenChange={() => {
                    onClose();
                }}
                layer="$modal"
                modal
                isMobile={isInMobileView}
                width={!isInMobileView ? '1000px' : '100%'}
            >
                <ModalDialogTitle>{t('title', 'Log Aktivitas')}</ModalDialogTitle>
                <ModalDialogContent
                    css={{
                        overflowY: 'auto',
                        height: 'auto',
                        maxHeight: '80vh',
                    }}
                >
                    <Flex
                        justify="between"
                        direction={isInMobileView ? 'column' : 'row'}
                        gap={5}
                        css={{ width: '100%' }}
                    >
                        <Flex align="center" gap={3}>
                            <CircleInfoOutline />
                            <Paragraph
                                css={{
                                    flex: 1,
                                    width: '100%',
                                }}
                            >
                                {isInMobileView
                                    ? t('mobileInfo', 'Menampilkan data maksimal 14 hari terakhir')
                                    : t('info', 'Menampilkan seluruh aktivitas yang dilakukan dalam 14 hari terakhir')}
                            </Paragraph>
                        </Flex>
                        <InputSearchbox
                            withDebounce
                            onChange={value => setFilter({ search: value })}
                            css={{
                                width: isInMobileView ? '100%' : '240px',
                            }}
                        />
                    </Flex>
                    <Table
                        id="activity-log-table"
                        columns={tableColumns(t, onShowDetail)}
                        totalData={filteredActivityLogs.length}
                        data={filteredActivityLogs}
                        searchQuery={filter.search}
                        css={{ marginTop: '$spacing-03', maxHeight: 'calc(80vh - 160px)' }}
                    />
                </ModalDialogContent>
                <ModalDialogFooter
                    css={{
                        justifyContent: 'center',
                        '@lg': {
                            justifyContent: 'flex-end',
                        },
                    }}
                >
                    <Button type="button" buttonType="ghost" size="md" onClick={onClose}>
                        {t('translation:label.close', 'Tutup')}
                    </Button>
                </ModalDialogFooter>
            </ModalDialog>
            {detailDialogState.open && (
                <DetailDialog
                    isOpen={detailDialogState.open}
                    onClose={() => setDetailDialogState({ open: false })}
                    logData={detailDialogState.logData}
                    t={t}
                />
            )}
        </React.Fragment>
    );
};

ActivityLogDialog.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    idCabang: PropTypes.string,
    filterBranch: PropTypes.string,
    logType: PropTypes.oneOf(Object.values(ACTIVITY_LOG_TYPE)),
};

ActivityLogDialog.defaultProps = {
    idCabang: '',
    filterBranch: '',
    logType: ACTIVITY_LOG_TYPE.PRODUCT,
};

export default ActivityLogDialog;
