import React, { createContext, useContext } from 'react';
import { useReminders } from '../hooks/useReminders';
import useFilterDialog from '../hooks/useFilterDialog';

const ReminderContext = createContext();

/**
 * Provider component that provides shared reminder state
 * This ensures all components use the same state instance
 */
export const ReminderProvider = ({ children, ...props }) => {
    const filterDialogState = useFilterDialog();
    
    const reminderState = useReminders(props, filterDialogState.filterDialog);

    return (
        <ReminderContext.Provider value={{ ...reminderState, ...filterDialogState }}>
            {children}
        </ReminderContext.Provider>
    );
};

/**
 * Custom hook to access the shared reminder state
 * Throws an error if used outside of ReminderProvider
 */
export const useReminderContext = () => {
    const context = useContext(ReminderContext);
    
    if (!context) {
        throw new Error('useReminderContext must be used within a ReminderProvider');
    }
    
    return context;
};
