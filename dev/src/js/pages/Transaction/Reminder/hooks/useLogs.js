import { useState, useEffect } from 'react';
import moment from 'moment';
import { Trans } from 'react-i18next';
import { getLogActivity } from '~/data/log';

const useLogs = ({ t, merchantId }) => {
    const defaultEmptyLogs = {
        description: t('label.noData', 'Tidak ada data'),
        isExpired: true,
    };

    const [logs, setLogs] = useState({
        list: [defaultEmptyLogs],
        last: defaultEmptyLogs,
        total: 1,
    });
    const [isShowLogs, setIsShowLogs] = useState(false);
    const [error, setError] = useState(null);

    const mapperLog = data => data.map(log => {
        const { activity_datetime: date, detail:
            {
                actor_name: actor,
                transaction_no: transactionNo,
                transaction_type_name: transactionType,
                product_name: productName,
            },
        } = log;
        return {
            description: (
                <Trans
                    t={t}
                    i18nKey={'log.markedAsReminded'}
                    values={{
                        actor,
                        transactionNo,
                        transactionType,
                        productName,
                    }}
                />
            ),
            date,
        };
    });

    const handleFetchActivityLog = async () => {
        const today = moment();
        const lastTwoWeeks = today.clone().subtract(14, 'days');

        const payload = {
            key_id: `transaction-reminder-${merchantId}`,
            show_detail: true,
            // limit: 10,
            date: lastTwoWeeks.format('YYYY-MM-DD'),
            end_date: today.format('YYYY-MM-DD'),
            sort_type: 'desc',
        };

        try {
            const { data, meta } = await getLogActivity(payload);
            const mappedLogs = data.length ? mapperLog(data) : [defaultEmptyLogs];
            setLogs({
                list: mappedLogs,
                last: mappedLogs[0],
                total: meta.total,
            });
        } catch(e) {
            console.log(e);
        }
    };

    useEffect(() => {
        if (merchantId && isShowLogs) {
            handleFetchActivityLog();
        }
    }, [merchantId, isShowLogs]);

    return { logs, isShowLogs, setIsShowLogs, handleFetchActivityLog };
};

export default useLogs;