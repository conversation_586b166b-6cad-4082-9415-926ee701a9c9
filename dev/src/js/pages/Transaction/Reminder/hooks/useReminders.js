import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { useMediaQuery } from '~/utils/useMediaQuery';
import { useSearchState } from '~/hooks/useSearchState';
import { getStatusOptionByValue, CUSTOMER_URL, TRANSACTION_URL } from '../constants';
import { getReminders, patchReminder } from '../services/reminderService';

export const useReminders = (props, filterDialog = {}) => {
    const { t, ready } = useTranslation(['Transaction/reminder', 'translation']);
    const isMobile = useMediaQuery('(max-width: 767px)');

    const [reminders, setReminders] = useState([]);
    const [remindersMeta, setRemindersMeta] = useState({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
    });
    const [isLoading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    /** 
     * start filters state: +filterDialog from props
    **/
   const { calendar, filterBranch } = props;
    const [filters, setFilters] = useState({
        status: getStatusOptionByValue('', t),
        date: [
            moment(calendar.start, 'DD/MM/YYYY').toDate(),
            moment(calendar.end, 'DD/MM/YYYY').toDate(),
        ],
    });
    const { keyword, onKeywordChange } = useSearchState();
    /** end filters state */

    const loadReminders = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        const { pageIndex, pageSize, sortAccessor, sortDirection } = params;
        
        try {
            const apiParams = {
                page: pageIndex + 1,
                limit: pageSize || 10,
                ... keyword && { search: keyword },
                start_date: moment(filters.date[0]).format('YYYY-MM-DD'),
                end_date: moment(filters.date[1]).format('YYYY-MM-DD'),
                ... filters.status.value !== '' && { is_reminded: filters.status.value },
                /**
                 * TODO:
                 * sort_accessor: sortAccessor,
                 * sort_direction: sortDirection,
                 * order_type_id -> payload belum ada?
                 */
                ... filterBranch && { outlet_id: filterBranch },
                ... filterDialog.orderTypes?.length > 0 && { order_type_id: filterDialog.orderTypes.join(',') },
                ... filterDialog.productCategories?.length > 0 && { product_category_id: filterDialog.productCategories.join(',') },
                ... filterDialog.customerGroups?.length > 0 && { customer_category_id: filterDialog.customerGroups.join(',') },
            };
                        
            const { data, meta } = await getReminders(apiParams);
            
            setReminders(data);
            setRemindersMeta({
                page: meta.current_page,
                limit: meta.per_page,
                total: meta.total,
                totalPages: meta.last_page,
            });
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [keyword, filters.status.value, filters.date, filterDialog, filterBranch]);

    // Apply filters and reload data from API
    const applyFilters = useCallback((newFilters) => {
        const updatedFilters = { ...filters, ...newFilters };
        setFilters(updatedFilters);
    }, [filters]);

    // Mark a reminder as reminded
    const markAsReminded = useCallback(async (reminderId) => {
        setLoading(true);
        setError(null);
        
        try {
            await patchReminder(JSON.stringify({ transaction_detail_id: reminderId }));
            
            // Update the reminder in the local state
            setReminders(prev => 
                prev.map(reminder => 
                    reminder.transaction_detail_id === reminderId
                        ? { ...reminder, is_reminded: true, reminder_total: reminder.reminder_total + 1 }
                        : reminder
                )
            );
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, []);

    const goToCustomerDetails = useCallback((customerId) => {
        const customerUrl = `${CUSTOMER_URL}/${customerId}`;
        const fullUrl = `${window.location.origin}${customerUrl}`;
        window.open(fullUrl, '_blank', 'noopener,noreferrer');
    }, []);

    const goToTransactionDetails = useCallback((transactionId) => {
        const transactionUrl = `${TRANSACTION_URL}/${transactionId}`;
        const fullUrl = `${window.location.origin}${transactionUrl}`;
        window.open(fullUrl, '_blank', 'noopener,noreferrer');
    }, []);

    // Clear error
    const clearError = useCallback(() => {
        setError(null);
    }, []);

    useEffect(() => {
        if (ready) {
            applyFilters({ status: getStatusOptionByValue('', t) });
        }
    }, [ready]);

    useEffect(() => {
        loadReminders({ pageIndex: 0 });
    }, [loadReminders]);

    return {
        // Translation
        t,

        // Media
        isMobile,

        // State
        reminders,
        remindersMeta,
        isLoading,
        filters,
        keyword,
        
        // Actions
        loadReminders,
        applyFilters,
        markAsReminded,
        onKeywordChange,
        goToCustomerDetails,
        goToTransactionDetails,

        // shared props
        props,
    };
}; 