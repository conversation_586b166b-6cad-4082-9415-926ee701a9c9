import { useState, useEffect, useMemo } from 'react';
import { getTransactionType } from '~/data/transactionType';
import { getKategoriProduk } from '~/data/product';
import { getGroupCustomer } from '~/data/customers';

const defaultFilterDialog = {
    orderTypes: [],
    productCategories: [],
    customerGroups: [],
};

const useFilterDialog = () => {
    const [isShowFilterDialog, setIsShowFilterDialog] = useState(false);
    const [filterDialog, setFilterDialog] = useState(defaultFilterDialog);

    /* Temporary state */
    const [isEditing, setIsEditing] = useState(false);
    const [tempFilterDialog, setTempFilterDialog] = useState(defaultFilterDialog);

    const [filterDialogOptions, setFilterDialogOptions] = useState({
        orderTypes: [],
        productCategories: [],
        customerGroups: [],
    });

    const completedSteps = useMemo(() => {
        return [
            tempFilterDialog.orderTypes.length > 0,
            tempFilterDialog.productCategories.length > 0,
            tempFilterDialog.customerGroups.length > 0,
        ];
    }, [tempFilterDialog]);

    const countCompletedSteps = useMemo(() => {
        return completedSteps.filter(Boolean).length;
    }, [completedSteps]);

    const updateTempFilterDialog = (key, value) => {
        setTempFilterDialog({ ...tempFilterDialog, [key]: value });
        setIsEditing(true);
    };

    // Reset temporary state to current filterDialog
    const resetTempFilterDialog = () => {
        setTempFilterDialog(defaultFilterDialog);
        if (!isEditing) {
            setIsEditing(true);
        }
    };

    const cancelUpdateTempFilterDialog = () => {
        if (isEditing) {
            setTempFilterDialog(filterDialog);
            setIsEditing(false);
        }
        setIsShowFilterDialog(false);
    };

    // Apply temporary state to actual state
    const applyTempFilterDialog = () => {
        if (isEditing) {
            setFilterDialog(tempFilterDialog);
            setIsEditing(false);
        }
        setIsShowFilterDialog(false);
    };

    const fetchTransactionType = async () => {
        try {
            const { data } = await getTransactionType();
            setFilterDialogOptions(prev => ({ ...prev, orderTypes: data }));
        } catch (error) {
            console.error(error);
        }
    };
    
    const fetchKategoriProduk = async () => {
        try {
            const { data } = await getKategoriProduk();
            const mappedData = data.map((item) => ({
                id: item.id_category_item,
                name: item.category_item_name,
            }));
            setFilterDialogOptions(prev => ({ ...prev, productCategories: mappedData }));
        } catch (error) {
            console.error(error);
        }
    };
    
    
    const fetchGroupCustomer = async () => {
        try {
            const { data } = await getGroupCustomer({ is_cms: 1 });
            const mappedData = data.map((item) => ({
                id: item.id_category_customer,
                name: item.category_customer_name,
            }));
            setFilterDialogOptions(prev => ({ ...prev, customerGroups: mappedData }));
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        if (isShowFilterDialog && !filterDialogOptions.orderTypes.length && !filterDialogOptions.productCategories.length && !filterDialogOptions.customerGroups.length) {
            fetchTransactionType();
            fetchKategoriProduk();
            fetchGroupCustomer();
        }
    }, [isShowFilterDialog, filterDialogOptions]);

    return {
        isShowFilterDialog,
        setIsShowFilterDialog,
        filterDialog,
        filterDialogOptions,
        completedSteps,
        countCompletedSteps,

        // Temporary state management
        tempFilterDialog,
        updateTempFilterDialog,
        resetTempFilterDialog,
        cancelUpdateTempFilterDialog,
        applyTempFilterDialog,
    };
};

export default useFilterDialog;