export const CUSTOMER_URL = '/pelanggan/daftar-pelanggan';
export const TRANSACTION_URL = '/laporan/penjualan/transaksi-v2';

/**
 * Status options for transaction reminders
 * Used for filtering and displaying reminder status
 */
export const REMINDER_STATUS_OPTIONS = t => [
    {
        name: t('statusOptions.all'),
        value: '',
    },
    {
        name: t('statusOptions.notRemindedYet'),
        value: false,
    },
    {
        name: t('statusOptions.haveBeenReminded'),
        value: true,
    }
];

/**
 * Get status option by value
 * @param {boolean|string} value - The status value
 * @returns {Object|null} The status option object or null if not found
 */
export const getStatusOptionByValue = (value, t) => {
    return REMINDER_STATUS_OPTIONS(t).find(option => option.value === value) || null;
};
