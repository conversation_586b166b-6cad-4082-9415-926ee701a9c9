import React from 'react';
import { foundations } from '@majoo-ui/core';
import { Flex, Box, Heading, IconButton, Button } from '@majoo-ui/react';
import { FilterOutline, LogOutline } from '@majoo-ui/icons';
import { FavoriteWrapper, TooltipGuidance } from '~/components/retina';
import ActivityLogModal from '~/components/activityLog/ActivityLogModal';
import userUtils from '~/utils/user.util';
import { useReminderContext } from '../context/ReminderContext';
import useLogs from '../hooks/useLogs';
import FilterGroupDialog from './FilterGroupDialog';

const { colors } = foundations;

const styles = {
    container: {
        '@md': {
            marginBottom: '20px',
        },
    },
};

const PageHeader = () => {
    const { t, setIsShowFilterDialog } = useReminderContext();
    const merchantId = userUtils.getLocalConfigByKey('parentId');
    const { logs, isShowLogs, setIsShowLogs, handleFetchActivityLog } = useLogs({ t, merchantId });

    const handleOpenLogs = () => {
        setIsShowLogs(true);
    };

    const handleToggleOpenFilterDialog = () => {
        setIsShowFilterDialog(prev => !prev);
    };

    return (
        <>
            <Flex justify="between" css={styles.container}>
                <Box>
                    <FavoriteWrapper>
                        <Heading heading="pageTitle">
                            {t('title', 'Pengingat Transaksi')}
                        </Heading>
                        <TooltipGuidance />
                    </FavoriteWrapper>
                </Box>
                <Flex gap={5}>
                    <Box onClick={handleOpenLogs} css={{ paddingTop: '4px' }}>
                        <IconButton>
                            <LogOutline size={20} color={colors.iconGreen} />
                        </IconButton>
                    </Box>
                    <Button
                        buttonType="secondary"
                        size="sm"
                        leftIcon={<FilterOutline color={colors.iconGreen} />}
                        onClick={handleToggleOpenFilterDialog}
                    >
                        {t('btnFilter', 'Filter Data')}
                    </Button>
                </Flex>
            </Flex>
            <ActivityLogModal
                open={isShowLogs}
                handleOpen={setIsShowLogs}
                activityLog={logs}
                handleFetchActivityLog={handleFetchActivityLog}
            />
            <FilterGroupDialog />
        </>
    );
};

export default PageHeader;