import React from 'react';
import { Table } from '@majoo-ui/react';
import { useReminderContext } from '../context/ReminderContext';
import { tableColumns } from '../columns';

const ReminderTable = () => {
    const {
        t,
        reminders,
        remindersMeta,
        keyword,
        loadReminders,
        isLoading,
        markAsReminded,
        goToCustomerDetails,
        goToTransactionDetails,
    } = useReminderContext();

    return (
        <Table
            columns={tableColumns(t, markAsReminded, goToCustomerDetails, goToTransactionDetails)}
            data={reminders}
            isLoading={isLoading}
            totalData={remindersMeta.total}
            pageIndex={remindersMeta.page}
            limit={remindersMeta.limit}
            fetchData={loadReminders}
            searchQuery={keyword}
        />
    );
};

export default ReminderTable; 