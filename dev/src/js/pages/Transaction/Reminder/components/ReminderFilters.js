import React from 'react';
import { Box, Flex, InputSearchbox, InputDatePicker, InputSelect } from '@majoo-ui/react';
import { useReminderContext } from '../context/ReminderContext';
import { REMINDER_STATUS_OPTIONS, getStatusOptionByValue } from '../constants';
import { styles } from './reminderFilters.styles';

const ReminderFilters = () => {
    const {
        t,
        filters,
        applyFilters,
        onKeywordChange,
        props,
    } = useReminderContext();

    const { assignCalendar } = props;

    const handleDateRangeChange = (date) => {
        assignCalendar(date[0], date[1]);
        applyFilters({ date });
    };

    const handleStatusChange = ({ value }) => {
        applyFilters({ status: getStatusOptionByValue(value, t) });
    };

    return (
        <Box
            css={styles.container}
        >
            <Flex
                justify="start"
                gap={3}
                css={styles.filtersContainer}
            >
                <Box css={styles.inputSearchbox}>
                    <InputSearchbox
                        placeholder={t('placeholder.search', { ns: 'translation' })}
                        onChange={onKeywordChange}
                    />
                </Box>
                <InputDatePicker
                    type="advance"
                    onChange={handleDateRangeChange}
                    date={filters.date}
                    size="sm"
                    css={styles.inputDatePicker}
                />
                <Box css={styles.inputSelect}>
                    <InputSelect
                        size="sm"
                        value={filters.status}
                        onChange={handleStatusChange}
                        option={REMINDER_STATUS_OPTIONS(t)}
                    />
                </Box>
            </Flex>
        </Box>
    );
};

export default ReminderFilters; 