import React from 'react';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    Box,
    Button,
    StepWizard,
    FormGroup,
    FormLabel,
    InputSelectTag,
    Flex,
} from '@majoo-ui/react';

import { useReminderContext } from '../../context/ReminderContext';
import { modalStyles, contentStyles, footertStyles, navStyles } from './filterGroupDialog.styles';
import { navContents } from './filterGroupDialog.data';

const FilterGroupDialog = () => {
    const {
        t,
        isMobile,
        isShowFilterDialog,
        completedSteps,
        countCompletedSteps,
        tempFilterDialog,
        updateTempFilterDialog,
        resetTempFilterDialog,
        cancelUpdateTempFilterDialog,
        applyTempFilterDialog,
        filterDialogOptions,
    } = useReminderContext();

    const changeOrderTypes = (value) => {
        updateTempFilterDialog('orderTypes', value);
    };
    const changeProductCategories = (value) => {
        updateTempFilterDialog('productCategories', value);
    };
    const changeCustomerGroups = (value) => {
        updateTempFilterDialog('customerGroups', value);
    };

    return (
        <ModalDialog
            isMobile={isMobile}
            size="xl"
            css={modalStyles}
            open={isShowFilterDialog}
            onOpenChange={cancelUpdateTempFilterDialog}
        >
            <ModalDialogTitle>Filter Laporan</ModalDialogTitle>
            <ModalDialogContent>
                <Box css={contentStyles}>
                    <StepWizard
                        isMobile={isMobile}
                        cssNav={navStyles}
                        navContents={navContents}
                        completedSteps={completedSteps}
                        initialStep={1}
                        onNavClicked={() => {}}
                    >
                        <FormGroup>
                            <FormLabel>Jenis Order</FormLabel>
                            <InputSelectTag
                                onChange={changeOrderTypes}
                                option={filterDialogOptions.orderTypes}
                                selectAllLabel="Pilih Semua"
                                showSelectAll
                                value={tempFilterDialog.orderTypes}
                            />
                        </FormGroup>
                        <FormGroup>
                            <FormLabel>Jenis Kategori</FormLabel>
                            <InputSelectTag
                                onChange={changeProductCategories}
                                option={filterDialogOptions.productCategories}
                                selectAllLabel="Pilih Semua"
                                showSelectAll
                                value={tempFilterDialog.productCategories}
                            />
                        </FormGroup>
                        <FormGroup>
                            <FormLabel>Grup Pelanggan</FormLabel>
                            <InputSelectTag
                                onChange={changeCustomerGroups}
                                option={filterDialogOptions.customerGroups}
                                selectAllLabel="Pilih Semua"
                                showSelectAll
                                value={tempFilterDialog.customerGroups}
                            />
                        </FormGroup>
                    </StepWizard>
                </Box>
            </ModalDialogContent>
            <ModalDialogFooter>
                <Flex justify="between" css={footertStyles}>
                    <Button buttonType="negative-secondary" onClick={resetTempFilterDialog} disabled={countCompletedSteps === 0}>
                        Reset
                    </Button>
                    <Flex gap={5}>
                        <Button buttonType="ghost" onClick={cancelUpdateTempFilterDialog}>
                            {t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                        </Button>
                        <Button buttonType="primary" onClick={applyTempFilterDialog}>
                            {`${t('label.set', { ns: 'translation', defaultValue: 'Atur' })} ${countCompletedSteps > 0 ? `(${countCompletedSteps})` : ''}`}
                        </Button>
                    </Flex>
                </Flex>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

export default FilterGroupDialog;
