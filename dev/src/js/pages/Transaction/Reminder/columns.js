/**
 * identifier: transaction_detail_id
 */
import React from 'react';
import { RowMenuColumn } from '@majoo-ui/react';
import BigProductName from '~/components/retina/BigProductName';
import ProductVariants from '~/components/retina/ProductVariants';
import { DateColumn, StatusColumn } from '~/components/retina'
import { getStatusOptionByValue } from './constants';

export const tableColumns = (t, markAsReminded, goToCustomerDetails, goToTransactionDetails) =>[
    {
        Header: t('tableHeaders.productName'),
        accessor: 'product_name',
        Cell: ({ value, row }) => {
            const { product_variants: productVariants = {} } = row.original;
            const variants = Object.values(productVariants);
            return (
                <>
                    <BigProductName
                        name={value}
                        withTooltip
                        maxLength={20}
                    />
                    <ProductVariants
                        data={variants}
                    />
                </>
            )
        },
        unsortable: true,
        colMaxWidth: 150,
    },
    {
        Header: t('tableHeaders.transactionNo'),
        accessor: 'transaction_no',
        colMaxWidth: 150,
    },
    {
        Header: t('tableHeaders.customerName'),
        accessor: 'customer_name',
        unsortable: true,
        colMaxWidth: 150,
    },
    {
        Header: t('tableHeaders.customerPhoneNumber'),
        accessor: 'customer_phone_number',
        unsortable: true,
        colMaxWidth: 120,
    },  
    {
        Header: t('tableHeaders.paymentDate'),
        accessor: 'payment_date',
        colMaxWidth: 100,
        Cell: ({ value }) => {
            return <DateColumn value={value} />
        }
    },  
    {
        Header: t('tableHeaders.reminderStatus'),
        accessor: 'is_reminded',
        unsortable: true,
        colMaxWidth: 150,
        Cell: ({ value }) => {
            return (
                <StatusColumn
                    value={value}
                    customText={{
                        active: getStatusOptionByValue(true, t).name,
                        inactive: getStatusOptionByValue(false, t).name,
                    }}
                    css={{
                        maxWidth: '150px',
                    }}
                />
            );
        }
    },
    {
        Header: t('tableHeaders.reminderTotal'),
        accessor: 'reminder_total',
        colMaxWidth: 100,
    },
    // based on prd still hided
    // ,
    // {
    //     Header: t('tableHeaders.transactionTypeName'),
    //     accessor: 'transaction_type_name',
    //     unsortable: true,
    // },
    // TODO: actions
    RowMenuColumn([
        {
            title: t('tableActions.sendReminder', 'Kirim Pengingat'),
            onClick: ({ row: { original } }) => {
                console.log(original);
                markAsReminded(original.transaction_detail_id);
            },
        },
        {
            title: t('tableActions.customerDetails', 'Detail Pelanggan'),
            onClick: ({ row: { original } }) => {
                goToCustomerDetails(original.customer_name);
            },
        },
        {
            title: t('tableActions.transactionDetails', 'Detail Transaksi'),
            onClick: ({ row: { original } }) => {
                goToTransactionDetails(original.transaction_no);
            },
        }
    ]),
];