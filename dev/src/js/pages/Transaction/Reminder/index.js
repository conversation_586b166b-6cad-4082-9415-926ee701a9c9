import React from 'react';
import { Paper, Separator } from '@majoo-ui/react';
import CoreHOC from '~/core/CoreHOC';
import { ReminderProvider } from './context/ReminderContext';
import PageHeader from './components/PageHeader';
import ReminderFilters from './components/ReminderFilters';
import ReminderTable from './components/ReminderTable';

const MainPage = (props) => {

    return (
        <ReminderProvider {...props}>
            <Paper responsive>
                <PageHeader />
                <Separator />
                <ReminderFilters />
                <Separator />
                <ReminderTable />
            </Paper>
        </ReminderProvider>
    );
};

const TransactionReminder = CoreHOC(MainPage);

export default TransactionReminder;
