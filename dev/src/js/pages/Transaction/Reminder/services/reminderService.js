import { fetchApi } from '~/services/api';

const ENDPOINTS = {
    getReminders: 'v1/transaction-reminder',
    updateReminder: 'v1/transaction-reminder/mark-reminded',
};

export const getReminders = params => fetchApi(ENDPOINTS.getReminders, params, 'get', {
    serviceDomainType: 'ms-transaction',
    authType: 'bearer',
});

export const patchReminder = (payload) => fetchApi(ENDPOINTS.updateReminder, payload, 'patch', {
    serviceDomainType: 'ms-transaction',
    authType: 'bearer',
});
