import React, { useContext, useState } from 'react';
import { Flex, Heading, Button, IconButton } from '@majoo-ui/react';
import { LogOutline, PlusOutline } from '@majoo-ui/icons';
import ActivityLogDialog from '~/components/activityLogDialog/ActivityLogDialog';
import { FavoriteWrapper } from '../../../../components/retina';
import RecipeMasterContext from '../context/RecipeMasterContext';
import { colors, styled } from '../../../../stitches.config';
import { ImportButton } from './ImportButton';

const TitleHeading = styled(Heading, {
    display: 'flex',
    alignItems: 'center',
});

const TitleSection = () => {
    const {
        t,
        setModalState,
        isMobile,
        menuPrivilege,
        setConfirmModalState,
        handleExport,
        idCabang,
        filterBranch,
    } = useContext(RecipeMasterContext);
    const [openLogDialog, setOpenLogDialog] = useState(false);

    const handleExportClick = () => {
        setConfirmModalState({
            open: true,
            title: t('modal.export.title'),
            description: t('modal.export.description'),
            onConfirm: () => {
                handleExport();
            },
        });
    };

    return (
        <React.Fragment>
            <Flex
                justify="between"
                align={isMobile ? 'start' : 'center'}
                direction={isMobile ? 'column' : 'row'}
                gap={isMobile ? 5 : 0}
                css={{
                    paddingBottom: '$spacing-05',
                    '@lg': {
                        paddingBottom: '$spacing-07',
                    },
                }}
            >
                <FavoriteWrapper>
                    <TitleHeading heading="pageTitle" color="primary">
                        {t('title', 'Master Resep')}
                    </TitleHeading>
                </FavoriteWrapper>
                <Flex gap={5} css={{ width: isMobile ? '100%' : 'auto' }}>
                    <Flex
                        justify="end"
                        gap={4}
                        direction={isMobile ? 'column' : 'row'}
                        css={{ width: isMobile ? '100%' : 'auto' }}
                    >
                        <Flex align="center" gap={4} css={{ width: isMobile ? '100%' : 'auto' }}>
                            <IconButton
                                onClick={() => {
                                    setOpenLogDialog(true);
                                }}
                                css={{ marginTop: '4px' }}
                            >
                                <LogOutline size={20} color={colors.iconGreen} />
                            </IconButton>
                            {(menuPrivilege.isCanCreate ||
                                menuPrivilege.isCanUpdate ||
                                (menuPrivilege.isCanView && !menuPrivilege.isWhitelistMerchant)) && <ImportButton />}
                            <Button onClick={handleExportClick} size="sm" buttonType="ghost" css={{ flex: 1 }}>
                                {t('label.exportData', 'Ekspor Data', { ns: 'translation' })}
                            </Button>
                        </Flex>
                        {menuPrivilege.isCanCreate && (
                            <Button
                                onClick={() => setModalState({ formModalOpen: true })}
                                size="sm"
                                leftIcon={<PlusOutline color="currentColor" />}
                            >
                                {t('addRecipe', 'Tambah Resep')}
                            </Button>
                        )}
                    </Flex>
                </Flex>
            </Flex>
            {openLogDialog && (
                <ActivityLogDialog
                    isOpen={openLogDialog}
                    onClose={() => setOpenLogDialog(false)}
                    idCabang={idCabang}
                    filterBranch={filterBranch}
                />
            )}
        </React.Fragment>
    );
};

export default TitleSection;
